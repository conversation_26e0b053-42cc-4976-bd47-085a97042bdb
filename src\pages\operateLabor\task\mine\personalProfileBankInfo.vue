<template>
  <div
    class="bankInfo"
    style="
      height: 100vh;
      background: #f7fbfd;
      box-sizing: border-box;
    "
  >
    <NavBar left-text="返回" left-arrow @click-left="onClickLeft" />
    <div style="padding: 20px 10px 0">
      <div
      v-if="!bankCardNo"
      style="
        padding: 20px;
        box-sizing: border-box;
        background: linear-gradient(90deg, #41b7fd, #0088fe);
        color: #fff;
        border-radius: 20px;
        text-align: center;
      "
      @click="goBind"
    >
      <Icon style="margin-right: 10px" name="add-o" />添加银行卡（仅支持储蓄卡）
    </div>
    <div v-else>
      <div
        style="
          padding: 40px 30px;
          display: flex;
          align-items: center;
          box-sizing: border-box;
          background: linear-gradient(90deg, #41b7fd, #0088fe);
          color: rgb(255, 255, 255);
          border-radius: 20px;
        "
      >
        <Icon style="margin-right: 10px; font-size: 30px" name="card" />
        {{ bankName }}
        <span style="margin-left: 10px">尾号{{ bankCardNo.slice(-4) }}</span>
      </div>
      <div
        style="
          text-align: center;
          padding: 20px 0;
          background: #fefefe;
          margin-top: 20px;
          border: 1px solid #e1e2e4;
          border-radius: 10px;
        "
        @click="goBind"
      >
        <Icon name="exchange" />
        换绑银行卡
      </div>
    </div>
    </div>
  </div>
</template>
<script>
import { Icon, NavBar } from 'vant'
import handleError from 'kit/helpers/handleErrorH5'
import makeClient from 'kit/services/operateLabor/makeClient'
const client = makeClient()

export default {
  components: {
    Icon,
    NavBar
  },
  data() {
    return {
      name: '',
      bankCardNo: '',
      bankName: ''
    }
  },
  created() {
    this.init()
  },
  methods: {
    onClickLeft() {
      this.$router.back()
    },
    async init() {
      const { laborInfoId } = this.$route.query
      const [err, r] = await client.apiLaborLaborDetail(laborInfoId)
      if (err) return handleError(err)
      this.name = r.data.basicInfo.name
      this.bankCardNo = r.data.laborBankCard.bankCardNo
      this.bankName = r.data.laborBankCard.bankName
    },
    goBind() {
      this.$router.push({
        path: '/personalProfile/bankInfoBind',
        query: {
          name: this.name,
          bankCardNo: this.bankCardNo,
          bankName: this.bankName,
          laborInfoId: this.$route.query.laborInfoId
        }
      })
    }
  }
}
</script>
