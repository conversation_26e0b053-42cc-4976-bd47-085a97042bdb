import handleError from './handleErrorH5'
import makeClient from '../services/operateLabor/makeClient'
const client = makeClient()

const initWechatSDK = async () => {
  try {
    if (typeof wx === 'undefined') {
      await loadWeixinSDK()
    }

    let url = window.location.href

    const [err, r] = await client.getWechatSign(url)
    if (err) return

    wx.config({
      debug: process.env.NODE_ENV !== 'production', // 开发环境开启调试
      appId: r.data.appId,
      timestamp: r.data.timestamp,
      nonceStr: r.data.nonceStr,
      signature: r.data.signature,
      jsApiList: ['miniprogram.navigateTo'],
      openTagList: ['wx-open-launch-weapp'] // 如果需要使用开放标签
    })
    
    // 返回Promise以便外部知道初始化结果
    return new Promise((resolve, reject) => {
      wx.ready(() => {
        console.log('微信SDK初始化成功')
        resolve(true)
      })
      
      wx.error((err) => {
        console.error('微信SDK初始化失败', err)
        reject(err)
      })
    })
  } catch (error) {
    return false
  }
}

// 动态加载微信JS-SDK的函数
function loadWeixinSDK() {
  return new Promise((resolve, reject) => {
    if (typeof wx !== 'undefined') {
      resolve()
      return
    }

    const script = document.createElement('script')
    script.src = 'https://res.wx.qq.com/open/js/jweixin-1.6.0.js'
    script.onload = resolve
    script.onerror = reject
    document.head.appendChild(script)
  })
}

export default initWechatSDK