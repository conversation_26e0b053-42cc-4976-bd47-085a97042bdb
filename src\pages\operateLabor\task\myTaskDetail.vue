<template>
  <div>
    <NavBar left-text="返回" left-arrow @click-left="onClickLeft" />
    <div class="type-bar">
      <span
        :class="{ active: currType === 'detail' }"
        @click="handleChangeType('detail')"
        >任务详情</span
      >
      <span
        :class="{ active: currType === 'record' }"
        @click="handleChangeType('record')"
        >交付记录</span
      >
    </div>
    <div style="padding: 0 10px; height: calc(100vh - 185px); overflow: auto">
      <div v-if="currType === 'detail'">
        <Cell
          style="
            background-image: linear-gradient(179deg, #e9edff 3%, #f9faff 100%);
            border-radius: 4px;
          "
        >
          <div
            v-if="info.laborTaskStatus"
            class="status"
            :style="{
              background: getBgColor(info.laborTaskStatus)
            }"
          >
            {{ info.laborTaskStatus }}
          </div>
          <div
            style="
              display: flex;
              justify-content: space-between;
              align-items: center;
            "
          >
            <h2>{{ info.taskName }}</h2>
          </div>
          <div
            style="
              display: flex;
              flex-direction: column;
              color: #828b9b;
              font-size: 14px;
            "
          >
            <div style="color: #1e2228; font-size: 16px; font-weight: 600">
              <span style="color: #4f71ff">
                {{ formatterPrice(info.taskAmount) }}
              </span>
            </div>
            <span
              >任务时间：{{ formatter(info.taskStartTime) }} -
              {{ formatter(info.taskEndTime) }}</span
            >
          </div>
        </Cell>
        <Cell>
          <h2>用工企业</h2>
          <span class="name">{{ info.enterpriseInfoName }}</span>
        </Cell>
        <Cell>
          <h2>派工企业</h2>
          <span class="name">{{ info.corporationName }}</span>
        </Cell>
        <Cell>
          <div
            style="
              display: flex;
              justify-content: space-between;
              align-items: center;
            "
          >
            <h2>人员要求</h2>
            <span style="font-weight: 400; font-size: 14px; color: #4e5769"
              >招收人数：{{ info.requiredCount }}</span
            >
          </div>
          <span class="name"
            >{{ info.requiredGender }} | {{ info.requiredAge }} |
            {{ info.requiredEducation }}</span
          >
        </Cell>
        <Cell>
          <h2>任务地点</h2>
          <span class="name">{{ info.taskLocation }}</span>
        </Cell>
        <Cell>
          <h2>任务描述</h2>
          <span class="name">{{ info.taskDescription || '-' }}</span>
        </Cell>
      </div>
      <div v-else>
        <div v-if="deliveryList.length">
          <div v-for="(item, index) in deliveryList" :key="index">
            <div style="display: flex">
              <div
                style="display: flex; flex-direction: column; align-items: center"
              >
                <img
                  width="24"
                  src="../../../assets/images/weixin/shape.png"
                  alt=""
                />
                <div
                  class="divider"
                  style="width: 1px; height: 250px; background: #0082ff"
                ></div>
              </div>
              <div style="margin-left: 10px; color: #71788f">
                <div
                  style="
                    background: #deefff;
                    color: #0082ff;
                    width: 60px;
                    text-align: center;
                    border-radius: 10px;
                    margin-bottom: 20px;
                  "
                >
                  {{ formatterCount(index) }}
                </div>
                <div>交付时间 {{ item.deliverTime }}</div>
                <div style="margin: 10px 0">交付描述 {{ item.deliveryDesc }}</div>
                <div style="display: flex">
                  上传成果
                  <img
                    style="width: 100px; height: 100px; margin-left: 10px"
                    :src="formatterImg(item.fileId[0])"
                    alt=""
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="no-content">
          <img
            style="width: 40%"
            src="../../../assets/images/weixin/no-data.png"
          />
          <div style="text-align: center; margin-top: 10px">暂无交付记录</div>
        </div>
      </div>
    </div>

    <div
      style="
        width: 100vw;
        height: 40px;
        position: fixed;
        bottom: 0px;
        padding: 20px 0px;
        box-shadow: 0 -5px 8px 0 #282e3a0d;
      "
    >
      <div
        v-if="
          this.laborTaskStatus === 'IN_PROGRESS' &&
          (info.deliveryStatus === '未交付' ||
            info.deliveryStatus === '审核拒绝' ||
            info.deliveryStatus === '交付成功')
        "
        class="btn"
        @click="handleDelivery"
      >
        交付成果
      </div>
      <div
        v-if="info.deliveryStatus.includes('待审核')"
        class="btn"
        style="background: #bbc2d8; color: #fff"
      >
        已交付成功，等待用工方审核
      </div>
      <div
        v-if="
          this.laborTaskStatus === 'COMPLETED' ||
          info.deliveryStatus === '完成交付'
        "
        class="btn"
        style="background: #bbc2d8; color: #fff"
      >
        恭喜您，已完成任务啦
      </div>
    </div>
  </div>
</template>
<script>
import { NavBar, Cell, Icon } from 'vant'
import handleError from '../../../helpers/handleErrorH5'
import makeClient from '../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  components: {
    NavBar,
    Cell,
    Icon
  },
  data() {
    return {
      info: {},
      laborTaskStatus: '',
      currType: 'detail',
      deliveryList: []
    }
  },
  async created() {
    this.laborTaskStatus = this.$route.query.laborTaskStatus
    await this.init()
  },
  methods: {
    getBgColor(status) {
      const colorMap = {
        待确认: '#FF9A01',
        待审核: '#FF9A01',
        待接受: '#FF9A01',
        进行中: '#4F71FF',
        已完成: '#07BB06',
        已拒绝: '#F63939'
      }
      return colorMap[status]
    },
    async init() {
      const id = this.$route.query.id
      const [err, r] = await client.personalTaskGet(id)
      if (err) return handleError(err)
      this.info = r.data
    },
    async getRecord() {
      const [err, r] = await client.personalGetDelivery({
        body: {
          taskId: this.$route.query.id
        }
      })
      if (err) return handleError(err)
      this.deliveryList = r.data
    },
    handleChangeType(type) {
      this.currType = type
      if (type === 'detail') {
        this.init()
      } else {
        this.getRecord()
      }
    },
    onClickLeft() {
      this.$router.back()
    },
    formatter(time) {
      if (!time) return ''
      return time.slice(0, 10)
    },
    formatterPrice(value) {
      if (!value) return
      if (value.includes('-')) {
        return value.split('-')[0] + ' ' + '-' + ' ' + value.split('-')[1] + '元'
      } else if (value === '面议') {
        return '面议'
      } else {
        return value + '元'
      }
    },
    formatterCount(index) {
      if (index == 0) {
        return '首次'
      }
      return `第${index + 1}次`
    },
    formatterImg(id) {
      return `${window.env.apiPath}/api/public/previewFile/${id}`
    },
    handleDelivery() {
      this.$router.push({
        path: '/delivery',
        query: {
          taskId: this.$route.query.id
        }
      })
    }
  }
}
</script>
<style scoped>
.type-bar {
  height: 36px;
  display: flex;
  justify-content: space-around;
  font-size: 15px;
  margin: 10px 0;
}
.type-bar span {
  text-align: center;
  margin-right: 23px;
  font-weight: 400;
  padding: 6px 0;
}
.active {
  border-bottom: 2px solid #0082ff;
  color: #0082ff;
  font-weight: 500;
  padding: 6px 0;
}
.status {
  position: absolute;
  top: 0;
  right: 0;
  width: 64px;
  height: 24px;
  border-radius: 0 4px 0 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  clip-path: polygon(0% 0%, 100% 0%, 100% 100%, 10% 100%, 2% 20%);
  font-weight: 600;
  font-size: 12px;
  color: #ffffff;
  line-height: 16px;
}
h2 {
  font-size: 16px;
  font-weight: 600;
}
.name {
  color: #828b9b;
}
.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #0082fe;
  color: #fff;
  border-radius: 20px;
  margin: 0 30px;
  padding: 10px;
}
.no-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
}
::v-deep .van-cell {
  padding: 0;
}
::v-deep .van-cell__value {
  padding: 10px 16px;
}
</style>
