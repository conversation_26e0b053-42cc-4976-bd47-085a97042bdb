<template>
  <div class="profile-container">
    <NavBar left-text="返回" left-arrow @click-left="onClickLeft" />
    <!-- 头部区域 -->
    <div class="header-section">
      <div class="header-title">
        <span class="title-text">服务合同</span>
        <div class="switch-btn" @click="showPicker = true">
          <span>切换</span>
          <img src="../../../../assets/images/weixin/switch2.png" class="switch-icon" alt="切换" />
        </div>
      </div>
      <div class="contract-description">
        {{ currentContractName }}
      </div>
    </div>

    <!-- 菜单列表 -->
    <div class="menu-list">
      <div
        class="menu-item"
        v-for="(item, index) in profileList"
        :key="index"
        @click="handleClick(item)"
      >
        <span class="menu-text">{{ item.replace('：', '') }}</span>
        <Icon name="arrow" class="arrow-icon" />
      </div>
    </div>

    <!-- <div class="btn">提交个人信息</div> -->

    <Popup v-model="showPicker" position="bottom">
      <Picker
        class="picker"
        title="切换服务合同"
        show-toolbar
        :default-index="defaultIndex"
        :columns="laborContractList"
        @cancel="showPicker = false"
        @confirm="onConfirm"
      />
    </Popup>
  </div>
</template>
<script>
import { Icon, Popup, Picker, NavBar } from 'vant'
import handleError from 'kit/helpers/handleErrorH5'
import makeClient from 'kit/services/operateLabor/makeClient'
const client = makeClient()

export default {
  components: {
    Icon,
    Popup,
    Picker,
    NavBar
  },
  data() {
    return {
      laborContractList: [],
      currentContractName: '',
      currentLaborInfoId: '',
      showPicker: false,
      defaultIndex: 0,
      profileList: ['基本信息：', '项目经历：', '附件信息：', '银行卡信息：']
    }
  },
  created() {
    this.init()
  },
  methods: {
    onClickLeft() {
      this.$router.push('/task/mine')
    },
    async init() {
      const [err, r] = await client.apiLaborLaborContractList()
      if (err) return handleError(err)
      this.laborContractList = r.data.map(item => ({
        text: item.contractName,
        value: item.contractId,
        laborInfoId: item.laborInfoId
      }))
      this.currentContractName = this.laborContractList[0].text
      this.currentLaborInfoId = this.laborContractList[0].laborInfoId
      this.defaultIndex = this.laborContractList.findIndex(item => item.laborInfoId === this.currentLaborInfoId)
    },
    onConfirm(value, index) {
      this.currentContractName = value.text
      this.currentLaborInfoId = value.laborInfoId
      this.defaultIndex = index
      this.showPicker = false
    },
    handleClick(item) {
      if (item === '基本信息：') {
        this.$router.push({
          path: '/personalProfile/basicInfo',
          query: {
            laborInfoId: this.currentLaborInfoId
          }
        })
      } else if (item === '项目经历：') {
        this.$router.push({
          path: '/personalProfile/projectExperience',
          query: {
            laborInfoId: this.currentLaborInfoId
          }
        })
      } else if (item === '附件信息：') {
        this.$router.push({
          path: '/personalProfile/attachmentInfo',
          query: {
            laborInfoId: this.currentLaborInfoId
          }
        })
      } else {
        this.$router.push({
          path: '/personalProfile/bankInfo',
          query: {
            laborInfoId: this.currentLaborInfoId
          }
        })
      }
    }
  }
}
</script>
<style scoped>
.profile-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 10px 0 0 0;
}

.header-section {
  background-image: linear-gradient(179deg, #e9edff 3%, #f9faff 100%);
  border-radius: 4px;
  padding: 16px;
  margin: 10px;
  margin-bottom: 12px;
}

.header-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.title-text {
  width: 56px;
  height: 20px;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 14px;
  color: #828B9B;
}

.switch-btn {
  display: flex;
  align-items: center;
  color: #4285f4;
  font-size: 14px;
  cursor: pointer;
}

.switch-icon {
  width: 16px;
  height: 16px;
  margin-left: 4px;
}

.contract-description {
  width: 303px;
  height: 44px;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  font-size: 14px;
  color: #333333;
  line-height: 1.4;
}

.menu-list {
  background-color: #ffffff;
  border-radius: 4px;
  margin: 10px;
  margin-bottom: 12px;
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0px 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  position: relative;
}

.menu-item::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 15px;
  right: 15px;
  height: 1px;
  background-color: #f0f0f0;
}

.menu-item {
  border-bottom: none;
}

.menu-item:last-child::after {
  display: none;
}

.menu-text {
  width: 303px;
  height: 44px;
  font-family: PingFangSC-Semibold;
  font-weight: 600;
  font-size: 14px;
  color: #333333;
  display: flex;
  align-items: center;
}

.arrow-icon {
  color: #c8c8c8;
  font-size: 14px;
  padding-right: 16px;
}

.btn {
  width: 300px;
  margin-bottom: 20px;
  margin-left: calc(50vw - 160px);
  padding: 10px 0;
  background: #2b7dfd;
  border-radius: 20px;
  text-align: center;
  color: #fff;
  position: fixed;
  bottom: 0;
}
</style>
