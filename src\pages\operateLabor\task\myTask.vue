<template>
  <div>
    <div class="type-bar">
      <span
        :class="{ active: currType === 'REVIEWING' }"
        @click="handleChangeType('REVIEWING')"
        >待确认</span
      >
      <span
        :class="{ active: currType === 'IN_PROGRESS' }"
        @click="handleChangeType('IN_PROGRESS')"
        >进行中</span
      >
      <span
        :class="{ active: currType === 'COMPLETED' }"
        @click="handleChangeType('COMPLETED')"
        >已完成</span
      >
      <span
        :class="{ active: currType === 'REJECTED' }"
        @click="handleChangeType('REJECTED')"
        >已拒绝</span
      >
    </div>
    <div
      style="
        min-height: calc(100vh - 66px);
        overflow: auto;
        box-sizing: border-box;
        padding: 20px 15px 10px 15px;
        background: #f8f8f8;
      "
    >
      <div v-if="taskList.length">
        <div
          class="task"
          v-for="(item, index) in taskList"
          :key="index"
          @click="handleDetail(item)"
        >
          <div style="display: flex; justify-content: space-between">
            <div style="display: flex; flex-direction: column">
              <span
                style="
                  width: 200px;
                  font-size: 16px;
                  font-weight: 600;
                  color: #262935;
                  margin-top: 7px;
                  display: -webkit-box;
                  -webkit-box-orient: vertical;
                  -webkit-line-clamp: 2;
                  overflow: hidden;
                  text-overflow: ellipsis;
                "
                >{{ item.taskName }}</span
              >
              <span
                style="
                  width: 200px;
                  white-space: pre-wrap;
                  font-size: 13px;
                  color: #71788f;
                  margin-top: 10px;
                "
                >{{ item.enterpriseInfoName || '-' }}</span
              >
            </div>
            <div style="font-size: 20px; font-weight: 600; color: #0082ff">
              {{ formatterPrice(item.taskAmount) }}
            </div>
          </div>
          <div
            style="
              display: inline-block;
              margin-top: 10px;
              padding: 4px 8px;
              background: #e2e8ff;
              border-radius: 4px;
            "
          >
            <span style="font-size: 12px; color: #4f71ff">{{
              item.taskTagName
            }}</span>
          </div>
          <div style="height: 1px; background: #f0f2f7; margin-top: 20px"></div>
          <div
            style="
              margin-top: 10px;
              display: flex;
              justify-content: space-between;
            "
          >
            <span style="font-size: 13px; color: #71788f; margin-top: 8px"
              >{{ formatterTime(item.taskStartTime) }} -
              {{ formatterTime(item.taskEndTime) }}</span
            >
            <span
              :style="{
                padding: '4px 8px',
                fontSize: '12px',
                fontWeight: '600',
                borderRadius: '4px',
                color: getColor(currType),
                background: getBgColor(currType)
              }"
              >{{ formatter(currType) }}</span
            >
          </div>
        </div>
      </div>
      <div class="no-content" v-else>
        <img
          style="width: 40%"
          src="https://olading-static-resource.oss-cn-beijing.aliyuncs.com/olading-mini-image/olading-front-h5/no-data.png"
        />
        <div style="text-align: center; margin-top: 10px">暂无任务</div>
      </div>
    </div>
  </div>
</template>
<script>
import wxProfile from '../../../helpers/wxProfile'
import handleError from '../../../helpers/handleErrorH5'
import makeClient from '../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  data() {
    return {
      currType: 'REVIEWING',
      taskList: []
    }
  },
  async created() {
    const data = await wxProfile()
    console.log('data===', data)
    if (!data.authStatus) {
      this.$router.push({
        path: '/ocr',
        query: {
          source: 'wx-myTask'
        }
      })
    } else {
      await this.fetchList()
    }
  },
  methods: {
    getColor(type) {
      const colorMap = {
        REVIEWING: '#FF9A01',
        IN_PROGRESS: '#4F71FF',
        COMPLETED: '#07BB06',
        REJECTED: '#F63939'
      }
      return colorMap[type]
    },
    getBgColor(type) {
      const colorMap = {
        REVIEWING: '#FFEDBA',
        IN_PROGRESS: '#C9DBFF',
        COMPLETED: '#BAF1B1',
        REJECTED: '#FDCCC4'
      }
      return colorMap[type]
    },
    formatterTime(time) {
      if (!time) return ''
      return time.slice(0, 10)
    },
    formatterPrice(value) {
      if (value.includes('-')) {
        return value.split('-')[0] + '+元'
      } else if (value === '面议') {
        return '面议'
      } else {
        return value + '元'
      }
    },
    formatter(currType) {
      if (currType === 'REVIEWING') {
        return '待确认'
      }
      if (currType === 'IN_PROGRESS') {
        return '进行中'
      }
      if (currType === 'COMPLETED') {
        return '已完成'
      }
      if (currType === 'REJECTED') {
        return '已拒绝'
      }
    },
    handleChangeType(type) {
      this.currType = type
      this.fetchList()
    },
    async fetchList() {
      this.taskList = []
      const [err, r] = await client.personalQueryTaskByStatus({
        body: {
          taskName: '',
          taskTagId: '',
          taskVisible: '',
          laborTaskStatus: this.currType
        }
      })
      if (err) return handleError(err)
      this.taskList = r.data
    },
    handleDetail(item) {
      this.$router.push({
        path: '/myTask/detail',
        query: {
          id: item.id,
          laborTaskStatus: this.currType
        }
      })
    }
  }
}
</script>
<style scoped>
.type-bar {
  display: flex;
  justify-content: space-between;
  font-size: 15px;
  padding: 20px 10px;
  height: 26px;
}
.type-bar span {
  text-align: center;
  margin-right: 23px;
  height: 25px;
}
.active {
  border-bottom: 2px solid #0082ff;
  color: #0082ff;
  font-weight: 600;
}
.task {
  padding: 15px;
  background: #fff;
  margin-bottom: 10px;
  position: relative;
  border-radius: 4px;
}
.no-content {
  height: 450px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
</style>
