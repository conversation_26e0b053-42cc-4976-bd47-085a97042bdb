<template>
  <article>
    <div class="view-picture" style="padding-top: 50px">
      <iframe
        :src="`/pdfViewer.html?pdf=${pdfUrl}`"
        style="width: 100vw; height: calc(100vh - 60px); border: none"
      ></iframe>
    </div>
    <div
      v-if="contractSignStatus !== 'SUCCESS'"
      style="
        position: fixed;
        bottom: 0;
        width: 100%;
        height: 50px;
        background: #fff;
        text-align: center;
      "
    >
      <Button
        style="
          width: 320px;
          background: rgb(79, 113, 255);
          border: none;
          border-radius: 20px;
        "
        type="primary"
        @click="handleSign"
        >签署</Button
      >
    </div>
  </article>
</template>
<script>
import { ImagePreview, Button } from 'vant'

export default {
  components: {
    ImagePreview,
    Button
  },
  data() {
    return {
      pdfUrl: '',
      contractSignStatus: ''
    }
  },
  created() {
    let archiveId = this.$route.query.archiveId
    // this.contractSignStatus = this.$route.query.contractSignStatus || ''
    this.pdfUrl = `${window.env?.apiPath}/api/public/previewFile/${archiveId}`
  },
  methods: {
    viewPicture() {
      ImagePreview([this.pdfUrl])
    },
    handleSign() {
      const source = this.$route.query.source || ''
      const taskId = this.$route.query.taskId || ''
      const protocolId = this.$route.query.protocolId || ''
      const currentType = this.$route.query.currentType || ''
      if (source) {
        this.$router.replace({
          path: '/faceAuth',
          query: {
            source: source,
            taskId: taskId,
            protocolId: protocolId,
            currentType: currentType
          }
        })
      } else {
        this.$router.push({
          path: '/laborContractSign',
          query: {
            protocolId: this.$route.query.protocolId
          }
        })
      }
    }
  }
}
</script>
